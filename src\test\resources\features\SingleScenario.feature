@TwoScenarios
Feature: Two Scenario Test for SauceLabs App
  As a tester
  I want to run two scenarios to verify the SauceLabs app functionality
  So that I can test both login and product catalog features

  Background:
    Given the SauceLabs app is launched and ready

  @Login @Smoke
  Scenario: Login with Standard User
    Given the SauceLabs app is launched
    When I generate locators for current screen
    And I enter username "standard_user"
    # And I enter password "secret_sauce"
    # And I click the login button
    # Then I should be logged in successfully

  @ProductCatalog @Smoke
  Scenario: View Product Catalog
    Given the SauceLabs app is launched
    When I login with standard user credentials
    And I view the product catalog
    Then I should see Sauce Labs Backpack
    And I should see Sauce Labs Bike Light
    And I should see the sort button
